import { Card } from 'primereact/card';
import { Badge } from 'primereact/badge';
import "./App.scss";

interface Service {
  name: string;
  url: string;
  description: string;
  icon: string;
  status?: 'active' | 'inactive' | 'maintenance';
}

function App() {
  const services: Service[] = [
    {
      name: "<PERSON><PERSON>",
      url: "https://grafana.msarknet.me",
      description: "Dashboard de métricas y monitorización",
      icon: "📊",
      status: "active"
    },
    {
      name: "Prometheus",
      url: "https://prom.msarknet.me",
      description: "Sistema de monitorización de métricas",
      icon: "📈",
      status: "active"
    },
    {
      name: "Traefik Dashboard",
      url: "https://traefik.msarknet.me",
      description: "Panel de control del proxy reverso",
      icon: "⚙️",
      status: "active"
    },
    {
      name: "Portainer",
      url: "https://portainer.msarknet.me",
      description: "Gestión de contenedores Docker",
      icon: "🐳",
      status: "maintenance"
    },
    {
      name: "Documentación",
      url: "https://docs.msarknet.me",
      description: "Documentación de proyectos y APIs",
      icon: "📚",
      status: "active"
    },
    {
      name: "API Backend",
      url: "https://msarknet.me/api",
      description: "Endpoint de prueba para desarrollo de APIs",
      icon: "🔧",
      status: "active"
    }
  ];

  const getStatusBadge = (status?: string) => {
    switch (status) {
      case 'active':
        return <Badge value="Activo" severity="success" className="ml-2" />;
      case 'maintenance':
        return <Badge value="Mantenimiento" severity="warning" className="ml-2" />;
      case 'inactive':
        return <Badge value="Inactivo" severity="danger" className="ml-2" />;
      default:
        return null;
    }
  };

  const serviceCardTemplate = (service: Service) => {
    return (
      <div className="service-card-content">
        <div className="flex align-items-center justify-content-between mb-3">
          <h3 className="service-title m-0">
            <span className="service-icon mr-2">{service.icon}</span>
            {service.name}
          </h3>
          {getStatusBadge(service.status)}
        </div>
        <p className="service-description text-600 mb-3">{service.description}</p>
        <a
          href={service.url}
          target="_blank"
          rel="noopener noreferrer"
          className="service-link"
        >
          Abrir Servicio →
        </a>
      </div>
    );
  };

  return (
    <div className="app-container">
      <div className="main-content max-w-6xl mx-auto p-4">
        {/* Header */}
        <div className="text-center mb-6">
          <h1 className="app-title text-6xl font-bold mb-3">🚀 MSarkNet</h1>
          <p className="app-subtitle text-xl text-600 mb-4">
            Entorno de Desarrollo Local con Traefik
          </p>

          {/* Status Badge */}
          <div className="status-container p-3 border-round-lg bg-green-50 border-1 border-green-200">
            <span className="text-green-800 font-semibold">
              ✅ Traefik Proxy funcionando correctamente
            </span>
            <br />
            <span className="text-green-600 text-sm">
              Todos los servicios están enrutados a través de HTTPS
            </span>
          </div>
        </div>

        {/* Services Grid */}
        <div className="grid">
          {services.map((service, index) => (
            <div key={index} className="col-12 md:col-6 lg:col-4">
              <Card
                className="service-card h-full cursor-pointer transition-all transition-duration-300 hover:shadow-4"
                pt={{
                  body: { className: 'p-4' },
                  content: { className: 'p-0' }
                }}
              >
                {serviceCardTemplate(service)}
              </Card>
            </div>
          ))}
        </div>

        {/* Tech Stack */}
        <div className="tech-stack-container mt-6 text-center">
          <h3 className="text-lg font-semibold mb-3">Stack Tecnológico:</h3>
          <div className="flex flex-wrap justify-content-center gap-2">
            {['Docker', 'Traefik', 'React', 'Node.js', 'Python', 'SSL/TLS', 'Nginx', 'PrimeReact'].map((tech) => (
              <Badge
                key={tech}
                value={tech}
                className="tech-badge p-2 text-sm"
                severity="info"
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

export default App;
