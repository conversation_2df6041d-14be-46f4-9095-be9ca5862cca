// Estilos personalizados para la aplicación de servicios
.app-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 2rem 1rem;
}

.main-content {
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  padding: 2rem;
  color: white;
}

// Header styles
.app-title {
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  color: white !important;
}

.app-subtitle {
  opacity: 0.9;
  color: white !important;
}

.status-container {
  background: rgba(0, 255, 0, 0.1) !important;
  border: 1px solid rgba(0, 255, 0, 0.3) !important;
}

// Service Cards
.service-card {
  background: rgba(255, 255, 255, 0.1) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  transition: all 0.3s ease !important;

  &:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.2) !important;
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.2) !important;
  }
}

.service-card-content {
  color: white;
}

.service-title {
  color: white !important;
  font-size: 1.2rem;
  font-weight: 600;
}

.service-icon {
  font-size: 1.3rem;
}

.service-description {
  color: rgba(255, 255, 255, 0.8) !important;
  font-size: 0.9rem;
  line-height: 1.4;
}

.service-link {
  color: #4fc3f7 !important;
  text-decoration: none;
  font-weight: 500;
  font-size: 0.9rem;
  transition: all 0.3s ease;

  &:hover {
    color: #29b6f6 !important;
    text-decoration: underline;
  }
}

// Tech Stack
.tech-stack-container {
  color: white;
  opacity: 0.9;
}

.tech-badge {
  background: rgba(255, 255, 255, 0.1) !important;
  color: white !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

// Badge customization
.p-badge {
  &.p-badge-success {
    background: rgba(76, 175, 80, 0.8) !important;
  }

  &.p-badge-warning {
    background: rgba(255, 152, 0, 0.8) !important;
  }

  &.p-badge-danger {
    background: rgba(244, 67, 54, 0.8) !important;
  }

  &.p-badge-info {
    background: rgba(33, 150, 243, 0.8) !important;
  }
}

// Responsive adjustments
@media (max-width: 768px) {
  .app-container {
    padding: 1rem 0.5rem;
  }

  .main-content {
    padding: 1.5rem;
  }

  .app-title {
    font-size: 3rem !important;
  }
}