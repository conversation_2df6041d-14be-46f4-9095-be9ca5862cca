{"name": "cerebro-fe", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "start": "vite --host 0.0.0.0", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"formik": "^2.4.6", "primeflex": "^4.0.0", "primeicons": "^7.0.0", "primereact": "^10.9.7", "react": "^19.1.1", "react-dom": "^19.1.1", "react-icons": "^5.5.0", "react-markdown": "^10.1.0"}, "devDependencies": {"@eslint/js": "^9.33.0", "@types/react": "^19.1.10", "@types/react-dom": "^19.1.7", "@vitejs/plugin-react-swc": "^4.0.0", "eslint": "^9.33.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "sass-embedded": "^1.92.1", "typescript": "~5.8.3", "typescript-eslint": "^8.39.1", "vite": "^7.1.2"}}