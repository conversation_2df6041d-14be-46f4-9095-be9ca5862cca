# ================================
# Dockerfile para React Frontend
# Ubicación: cerebro-fe/Dockerfile
# ================================

# Stage base - Node.js
FROM node:24.8.0-alpine3.22 AS base
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

# Stage de desarrollo
FROM node:24.8.0-alpine3.22 AS development
WORKDIR /app

# Instalar dependencias
COPY package*.json ./
RUN npm install

# Copiar código fuente
COPY . .

# Variables de entorno para desarrollo
ENV NODE_ENV=development

# Exponer puerto (Vite usa 5173 por defecto)
EXPOSE 5173

# Comando de desarrollo con hot reload
CMD ["npm", "start"]

# Stage de construcción
FROM node:24.8.0-alpine3.22 AS build
WORKDIR /app

# Copiar dependencias
COPY --from=base /app/node_modules ./node_modules
COPY package*.json ./

# Copiar código fuente y construir
COPY . .
RUN npm run build

# Stage de producción
FROM nginx:alpine AS production
WORKDIR /usr/share/nginx/html

# Remover archivos por defecto de nginx
RUN rm -rf ./*

# Copiar build de React
COPY --from=build /app/build .

# Configuración de nginx para SPA
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Exponer puerto
EXPOSE 80

# Comando para producción
CMD ["nginx", "-g", "daemon off;"]

# Stage por defecto (desarrollo)
FROM development
